<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use App\Models\User;
use App\Models\Role;
use App\Models\Region;
use App\Models\Dealer;
use App\Models\UserDealer;
use Exception;
use Carbon\Carbon;

class UserMigrationService
{
    private $backupPath = 'migrations/users';

    /**
     * MySQL'den users verilerini çek
     */
    public function fetchMySQLUsers(): array
    {
        try {
            return DB::connection('mysql')
                ->table('users')
                ->select('*')
                ->orderBy('id')
                ->get()
                ->toArray();
        } catch (Exception $e) {
            throw new Exception('MySQL\'den users verisi çekme hatası: ' . $e->getMessage());
        }
    }

    /**
     * MySQL'den user_branches2 verilerini çek
     */
    public function fetchMySQLUserBranches(): array
    {
        try {
            return DB::connection('mysql')
                ->table('user_branches2')
                ->select('user_id', 'branch_id')
                ->get()
                ->toArray();
        } catch (Exception $e) {
            throw new Exception('MySQL\'den user_branches2 verisi çekme hatası: ' . $e->getMessage());
        }
    }

    /**
     * User ID'ye göre branch_id'yi al
     */
    public function getUserBranchId($userId, $userBranches): ?int
    {
        foreach ($userBranches as $userBranch) {
            if ($userBranch->user_id == $userId) {
                return $userBranch->branch_id;
            }
        }
        return null;
    }

    /**
     * Mevcut PostgreSQL users verilerini yedekle
     */
    public function backupCurrentUsers(): string
    {
        $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
        $backupFile = "{$this->backupPath}/users_backup_{$timestamp}.json";

        $currentUsers = User::with(['role', 'dealer', 'region', 'dealers'])->get()->toArray();
        
        $backupData = [
            'timestamp' => $timestamp,
            'total_users' => count($currentUsers),
            'users' => $currentUsers,
            'metadata' => [
                'php_version' => PHP_VERSION,
                'laravel_version' => app()->version(),
                'database_connection' => config('database.default'),
                'created_by' => 'UserMigrationService',
            ]
        ];

        Storage::put($backupFile, json_encode($backupData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        return $backupFile;
    }

    /**
     * Yedekten geri yükle
     */
    public function restoreFromBackup(string $backupFile): bool
    {
        if (!Storage::exists($backupFile)) {
            throw new Exception("Yedek dosyası bulunamadı: {$backupFile}");
        }

        $backupData = json_decode(Storage::get($backupFile), true);
        
        if (!$backupData || !isset($backupData['users'])) {
            throw new Exception('Geçersiz yedek dosyası formatı');
        }

        DB::beginTransaction();
        try {
            // Mevcut verileri sil
            UserDealer::truncate();
            User::truncate();

            // Yedekten verileri geri yükle
            foreach ($backupData['users'] as $userData) {
                $user = User::create([
                    'name' => $userData['name'],
                    'email' => $userData['email'],
                    'phone' => $userData['phone'],
                    'password' => $userData['password'],
                    'role_id' => $userData['role_id'],
                    'dealer_id' => $userData['dealer_id'] ?? null,
                    'region_id' => $userData['region_id'] ?? null,
                    'email_verified_at' => $userData['email_verified_at'],
                    'remember_token' => $userData['remember_token'],
                    'created_at' => $userData['created_at'],
                    'updated_at' => $userData['updated_at'],
                ]);

                // User-dealer ilişkilerini geri yükle
                if (isset($userData['dealers']) && is_array($userData['dealers'])) {
                    foreach ($userData['dealers'] as $dealerData) {
                        UserDealer::create([
                            'user_id' => $user->id,
                            'dealer_id' => $dealerData['id'],
                        ]);
                    }
                }
            }

            DB::commit();
            return true;
        } catch (Exception $e) {
            DB::rollback();
            throw new Exception('Geri yükleme hatası: ' . $e->getMessage());
        }
    }

    /**
     * Mevcut yedek dosyalarını listele
     */
    public function listBackups(): array
    {
        $files = Storage::files($this->backupPath);
        $backups = [];

        foreach ($files as $file) {
            if (str_ends_with($file, '.json') && str_contains($file, 'users_backup_')) {
                $content = json_decode(Storage::get($file), true);
                $backups[] = [
                    'file' => $file,
                    'timestamp' => $content['timestamp'] ?? 'Unknown',
                    'total_users' => $content['total_users'] ?? 0,
                    'size' => Storage::size($file),
                    'created_at' => Storage::lastModified($file),
                ];
            }
        }

        // Tarihe göre sırala (en yeni önce)
        usort($backups, function($a, $b) {
            return $b['created_at'] <=> $a['created_at'];
        });

        return $backups;
    }

    /**
     * Eski yedek dosyalarını temizle
     */
    public function cleanOldBackups(int $keepCount = 5): int
    {
        $backups = $this->listBackups();
        $deletedCount = 0;

        if (count($backups) > $keepCount) {
            $toDelete = array_slice($backups, $keepCount);
            
            foreach ($toDelete as $backup) {
                Storage::delete($backup['file']);
                $deletedCount++;
            }
        }

        return $deletedCount;
    }

    /**
     * Migration istatistiklerini al
     */
    public function getMigrationStats(): array
    {
        try {
            $mysqlCount = DB::connection('mysql')->table('users')->count();
            $mysqlUserBranchesCount = DB::connection('mysql')->table('user_branches2')->count();
        } catch (Exception $e) {
            $mysqlCount = 'Bağlantı Hatası';
            $mysqlUserBranchesCount = 'Bağlantı Hatası';
        }

        $postgresqlCount = User::count();
        $userDealersCount = UserDealer::count();
        $backupsCount = count($this->listBackups());
        $rolesCount = Role::count();
        $dealersCount = Dealer::count();
        $regionsCount = Region::count();

        return [
            'mysql_users' => $mysqlCount,
            'mysql_user_branches' => $mysqlUserBranchesCount,
            'postgresql_users' => $postgresqlCount,
            'postgresql_user_dealers' => $userDealersCount,
            'available_roles' => $rolesCount,
            'available_dealers' => $dealersCount,
            'available_regions' => $regionsCount,
            'available_backups' => $backupsCount,
            'last_backup' => $this->getLastBackupInfo(),
        ];
    }

    /**
     * Son yedek bilgisini al
     */
    private function getLastBackupInfo(): ?array
    {
        $backups = $this->listBackups();
        return $backups[0] ?? null;
    }

    /**
     * Veri doğrulama
     */
    public function validateMigration(): array
    {
        $issues = [];

        try {
            $mysqlUsers = $this->fetchMySQLUsers();
            $postgresqlUsers = User::all();

            // Sayı kontrolü
            if (count($mysqlUsers) !== $postgresqlUsers->count()) {
                $issues[] = "Kayıt sayısı uyumsuzluğu: MySQL users({" . count($mysqlUsers) . "}) vs PostgreSQL users({$postgresqlUsers->count()})";
            }

            // Email kontrolü
            $mysqlEmails = array_column($mysqlUsers, 'email');
            $postgresqlEmails = $postgresqlUsers->pluck('email')->toArray();
            
            $missingInPostgres = array_diff($mysqlEmails, $postgresqlEmails);
            $extraInPostgres = array_diff($postgresqlEmails, $mysqlEmails);

            if (!empty($missingInPostgres)) {
                $issues[] = "PostgreSQL'de eksik email'ler: " . implode(', ', array_slice($missingInPostgres, 0, 5)) . (count($missingInPostgres) > 5 ? '...' : '');
            }

            if (!empty($extraInPostgres)) {
                $issues[] = "PostgreSQL'de fazla email'ler: " . implode(', ', array_slice($extraInPostgres, 0, 5)) . (count($extraInPostgres) > 5 ? '...' : '');
            }

            // Role kontrolü
            $usersWithoutRole = $postgresqlUsers->whereNull('role_id')->count();
            if ($usersWithoutRole > 0) {
                $issues[] = "Role'ü olmayan user sayısı: {$usersWithoutRole}";
            }

            // User-Dealer ilişki kontrolü
            $mysqlUserBranches = $this->fetchMySQLUserBranches();
            $postgresqlUserDealers = UserDealer::count();

            if (count($mysqlUserBranches) !== $postgresqlUserDealers) {
                $issues[] = "User-Dealer ilişki sayısı uyumsuzluğu: MySQL user_branches2({" . count($mysqlUserBranches) . "}) vs PostgreSQL user_dealers({$postgresqlUserDealers})";
            }

            // Region kontrolü
            $usersWithoutRegion = $postgresqlUsers->whereNull('region_id')->count();
            if ($usersWithoutRegion > 0) {
                $issues[] = "Region'u olmayan user sayısı: {$usersWithoutRegion}";
            }

        } catch (Exception $e) {
            $issues[] = "Doğrulama hatası: " . $e->getMessage();
        }

        return $issues;
    }

    /**
     * İlk role ID'sini al
     */
    public function getFirstRoleId(): ?int
    {
        $firstRole = Role::first();
        return $firstRole ? $firstRole->id : null;
    }

    /**
     * İlk region ID'sini al
     */
    public function getFirstRegionId(): ?int
    {
        $firstRegion = Region::first();
        return $firstRegion ? $firstRegion->id : null;
    }

    /**
     * Branch ID'ye göre dealer ID'sini al (MySQL branch_id = PostgreSQL dealer_id)
     */
    public function getDealerIdByBranchId($branchId): ?int
    {
        // MySQL'deki branch_id, PostgreSQL'deki dealer tablosunda aynı ID ile eşleşiyor
        $dealer = Dealer::find($branchId);
        return $dealer ? $dealer->id : null;
    }

    /**
     * Zone ID'ye göre region ID'sini al (MySQL zone_id = PostgreSQL region_id)
     */
    public function getRegionIdByZoneId($zoneId): ?int
    {
        // MySQL'deki zone_id, PostgreSQL'deki region tablosunda aynı ID ile eşleşiyor
        $region = Region::find($zoneId);
        return $region ? $region->id : null;
    }

    /**
     * User-Dealer ilişkilerini kaydet
     */
    public function saveUserDealerRelationships($userId, $mysqlUserBranches): int
    {
        $savedCount = 0;

        foreach ($mysqlUserBranches as $userBranch) {
            if ($userBranch->user_id == $userId) {
                $dealerId = $this->getDealerIdByBranchId($userBranch->branch_id);

                if ($dealerId) {
                    // Aynı ilişki zaten var mı kontrol et
                    $existingRelation = UserDealer::where('user_id', $userId)
                        ->where('dealer_id', $dealerId)
                        ->first();

                    if (!$existingRelation) {
                        UserDealer::create([
                            'user_id' => $userId,
                            'dealer_id' => $dealerId,
                        ]);
                        $savedCount++;
                    }
                }
            }
        }

        return $savedCount;
    }
}
