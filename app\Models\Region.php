<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Region extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * Get the dealers for the region (one-to-many relationship).
     */
    public function dealers()
    {
        return $this->hasMany(Dealer::class);
    }

    /**
     * Get the dealers for the region (many-to-many relationship through pivot table).
     */
    public function dealersMany()
    {
        return $this->belongsToMany(Dealer::class, 'region_dealers');
    }

    /**
     * Get the region dealers pivot records.
     */
    public function regionDealers()
    {
        return $this->hasMany(RegionDealer::class);
    }

    /**
     * Scope a query to only include active regions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Search scope for regions
     */
    public function scopeSearch($query, $q)
    {
        if (!$q) return $query;

        return $query->where(function($sub) use ($q) {
            $sub->where('name', 'ILIKE', "%$q%")
                ->orWhere('description', 'ILIKE', "%$q%");
        });
    }
}
