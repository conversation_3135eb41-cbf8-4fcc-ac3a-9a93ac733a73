<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Dealer extends Model
{
    use HasFactory;

    protected $fillable = [
        'region_id',
        'name',
        'contact_person',
        'phone',
        'email',
        'address',
        'city',
        'district',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * Get the region that owns the dealer (one-to-many relationship).
     */
    public function region()
    {
        return $this->belongsTo(Region::class);
    }

    /**
     * Get the regions for the dealer (many-to-many relationship through pivot table).
     */
    public function regionsMany()
    {
        return $this->belongsToMany(Region::class, 'region_dealers');
    }

    /**
     * Get the region dealers pivot records.
     */
    public function regionDealers()
    {
        return $this->hasMany(RegionDealer::class);
    }

    /**
     * Get the customers for the dealer.
     */
    public function customers()
    {
        return $this->hasMany(Customer::class);
    }

    /**
     * Get the users for the dealer (one-to-many relationship).
     */
    public function users()
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the users for the dealer (many-to-many relationship).
     */
    public function usersMany()
    {
        return $this->belongsToMany(User::class, 'user_dealers');
    }

    /**
     * Get the user dealers pivot records.
     */
    public function userDealers()
    {
        return $this->hasMany(UserDealer::class);
    }

    /**
     * Scope a query to only include active dealers.
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Search scope for dealers
     */
    public function scopeSearch($query, $q)
    {
        if (!$q) return $query;

        return $query->where(function($sub) use ($q) {
            $sub->where('name', 'ILIKE', "%$q%")
                ->orWhere('contact_person', 'ILIKE', "%$q%")
                ->orWhere('phone', 'ILIKE', "%$q%")
                ->orWhere('email', 'ILIKE', "%$q%")
                ->orWhere('city', 'ILIKE', "%$q%")
                ->orWhere('district', 'ILIKE', "%$q%")
                ->orWhereHas('region', function($regionQuery) use ($q) {
                    $regionQuery->where('name', 'ILIKE', "%$q%");
                });
        });
    }
}
