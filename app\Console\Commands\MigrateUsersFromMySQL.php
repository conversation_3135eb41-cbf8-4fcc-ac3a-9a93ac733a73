<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\Role;
use App\Services\UserMigrationService;
use Exception;

class MigrateUsersFromMySQL extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:users-from-mysql 
                            {--dry-run : <PERSON><PERSON>e önizleme yap, veri kaydetme}
                            {--force : Mevcut verileri sil ve yeniden yükle}
                            {--chunk=100 : Kaç kayıt gruplarında işle}
                            {--backup : Migration öncesi yedek al}
                            {--validate : Migration sonrası doğrulama yap}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'MySQL users tablosundan users verilerini PostgreSQL\'e taşır';

    private UserMigrationService $migrationService;

    public function __construct(UserMigrationService $migrationService)
    {
        parent::__construct();
        $this->migrationService = $migrationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 MySQL Users\'den PostgreSQL Users\'e Migration Başlatılıyor...');
        
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');
        $chunkSize = (int) $this->option('chunk');
        $backup = $this->option('backup');
        $validate = $this->option('validate');

        try {
            // Migration istatistiklerini göster
            $this->showMigrationStats();

            // Role kontrolü
            if (!$this->checkRoles()) {
                return 1;
            }

            // Yedek al
            if ($backup && !$dryRun) {
                $this->info('💾 Mevcut users verileri yedekleniyor...');
                $backupFile = $this->migrationService->backupCurrentUsers();
                $this->info("✅ Yedek oluşturuldu: {$backupFile}");
            }

            // MySQL ve PostgreSQL bağlantılarını test et
            $this->info('📡 Bağlantılar test ediliyor...');
            $this->testConnections();

            // Mevcut verileri kontrol et
            $existingCount = User::count();
            if ($existingCount > 0 && !$force) {
                $this->warn("⚠️  PostgreSQL'de {$existingCount} adet user kaydı bulundu.");
                if (!$this->confirm('Devam etmek istiyor musunuz? (Mevcut veriler korunacak)')) {
                    $this->info('❌ İşlem iptal edildi.');
                    return 0;
                }
            }

            // Force seçeneği ile mevcut verileri sil
            if ($force && $existingCount > 0) {
                $this->warn("🗑️  Force seçeneği aktif. {$existingCount} adet mevcut kayıt silinecek...");
                if (!$dryRun) {
                    User::truncate();
                    $this->info('✅ Mevcut veriler silindi.');
                }
            }

            // MySQL'den verileri çek
            $this->info('📥 MySQL\'den users verileri çekiliyor...');
            $mysqlUsers = $this->migrationService->fetchMySQLUsers();

            if (empty($mysqlUsers)) {
                $this->warn('⚠️  MySQL\'de users verisi bulunamadı.');
                return 0;
            }

            $this->info("📊 MySQL'de {" . count($mysqlUsers) . "} adet user bulundu.");

            // MySQL'den user_branches2 verilerini çek
            $this->info('📥 MySQL\'den user_branches2 verileri çekiliyor...');
            $mysqlUserBranches = $this->migrationService->fetchMySQLUserBranches();
            $this->info("📊 MySQL'de {" . count($mysqlUserBranches) . "} adet user_branches2 kaydı bulundu.");

            // Verileri işle
            $this->processUsers($mysqlUsers, $mysqlUserBranches, $dryRun, $chunkSize);

            // Doğrulama yap
            if ($validate && !$dryRun) {
                $this->info('🔍 Migration doğrulanıyor...');
                $issues = $this->migrationService->validateMigration();
                
                if (empty($issues)) {
                    $this->info('✅ Doğrulama başarılı. Tüm veriler doğru şekilde taşındı.');
                } else {
                    $this->warn('⚠️  Doğrulama sorunları:');
                    foreach ($issues as $issue) {
                        $this->warn("  - {$issue}");
                    }
                }
            }

            $this->info('🎉 Migration başarıyla tamamlandı!');
            
        } catch (Exception $e) {
            $this->error('❌ Hata: ' . $e->getMessage());
            $this->error('📍 Dosya: ' . $e->getFile() . ':' . $e->getLine());
            return 1;
        }

        return 0;
    }

    /**
     * Migration istatistiklerini göster
     */
    private function showMigrationStats()
    {
        $stats = $this->migrationService->getMigrationStats();
        
        $this->info('📊 Migration İstatistikleri:');
        $this->table(['Kaynak', 'Değer'], [
            ['MySQL Users', $stats['mysql_users']],
            ['PostgreSQL Users', $stats['postgresql_users']],
            ['Mevcut Roles', $stats['available_roles']],
            ['Mevcut Yedekler', $stats['available_backups']],
            ['Son Yedek', $stats['last_backup']['timestamp'] ?? 'Yok'],
        ]);
    }

    /**
     * Role kontrolü
     */
    private function checkRoles(): bool
    {
        $roleCount = Role::count();
        
        if ($roleCount === 0) {
            $this->error('❌ PostgreSQL\'de hiç role bulunamadı!');
            $this->error('💡 Önce roles migration\'ını çalıştırın: php artisan migrate:roles-from-mysql');
            return false;
        }

        $this->info("✅ {$roleCount} adet role bulundu.");
        return true;
    }

    /**
     * Bağlantıları test et
     */
    private function testConnections()
    {
        try {
            DB::connection('mysql')->getPdo();
            $this->info('✅ MySQL bağlantısı başarılı.');
        } catch (Exception $e) {
            throw new Exception('MySQL bağlantısı başarısız: ' . $e->getMessage());
        }

        try {
            DB::connection()->getPdo();
            $this->info('✅ PostgreSQL bağlantısı başarılı.');
        } catch (Exception $e) {
            throw new Exception('PostgreSQL bağlantısı başarısız: ' . $e->getMessage());
        }
    }

    /**
     * Users verilerini işle ve PostgreSQL'e kaydet
     */
    private function processUsers($mysqlUsers, $mysqlUserBranches, $dryRun, $chunkSize)
    {
        $chunks = array_chunk($mysqlUsers, $chunkSize);
        $totalProcessed = 0;
        $totalErrors = 0;

        $progressBar = $this->output->createProgressBar(count($mysqlUsers));
        $progressBar->start();

        foreach ($chunks as $chunk) {
            foreach ($chunk as $mysqlUser) {
                try {
                    $userData = $this->mapUserData($mysqlUser, $mysqlUserBranches);
                    
                    if ($dryRun) {
                        $this->line("\n🔍 Dry Run - İşlenecek veri:");
                        $this->table(['Alan', 'Değer'], [
                            ['ID (MySQL)', $mysqlUser->id ?? 'N/A'],
                            ['Name', $userData['name']],
                            ['Email', $userData['email']],
                            ['Phone', $userData['phone'] ?? 'N/A'],
                            ['Role ID', $userData['role_id']],
                            ['Dealer ID', $userData['dealer_id'] ?? 'N/A'],
                            ['Region ID', $userData['region_id'] ?? 'N/A'],
                            ['Zone ID (MySQL)', $mysqlUser->zone_id ?? 'N/A'],
                        ]);
                    } else {
                        // Aynı email'de user var mı kontrol et
                        $existingUser = User::where('email', $userData['email'])->first();
                        
                        if ($existingUser) {
                            $this->warn("\n⚠️  '{$userData['email']}' email'li user zaten mevcut. Atlanıyor...");
                        } else {
                            User::create($userData);
                            $totalProcessed++;
                        }
                    }
                    
                    $progressBar->advance();
                    
                } catch (Exception $e) {
                    $totalErrors++;
                    $this->error("\n❌ Hata (ID: {$mysqlUser->id}): " . $e->getMessage());
                    $progressBar->advance();
                }
            }
        }

        $progressBar->finish();
        $this->newLine(2);

        if ($dryRun) {
            $this->info("🔍 Dry Run tamamlandı. {" . count($mysqlUsers) . "} kayıt işlenebilir durumda.");
        } else {
            $this->info("✅ {$totalProcessed} kayıt başarıyla işlendi.");
            if ($totalErrors > 0) {
                $this->warn("⚠️  {$totalErrors} kayıtta hata oluştu.");
            }
        }
    }

    /**
     * MySQL user verisini PostgreSQL user formatına dönüştür
     */
    private function mapUserData($mysqlUser, $mysqlUserBranches)
    {
        // İlk role'ı al
        $roleId = $this->migrationService->getFirstRoleId();

        // MySQL zone_id'yi PostgreSQL region_id'ye çevir
        $regionId = null;
        if (isset($mysqlUser->zone_id) && $mysqlUser->zone_id) {
            $regionId = $this->migrationService->getRegionIdByZoneId($mysqlUser->zone_id);
        }
        // Eğer region bulunamazsa ilk region'ı al
        if (!$regionId) {
            $regionId = $this->migrationService->getFirstRegionId();
        }

        // MySQL user_branches2'den branch_id'yi al ve PostgreSQL dealer_id'ye çevir
        $dealerId = null;
        $branchId = $this->migrationService->getUserBranchId($mysqlUser->id, $mysqlUserBranches);
        if ($branchId) {
            $dealerId = $this->migrationService->getDealerIdByBranchId($branchId);
        }

        return [
            'name' => ($mysqlUser->name && $mysqlUser->surname)
                ? $mysqlUser->name . ' ' . $mysqlUser->surname
                : 'Bilinmeyen Kullanıcı',
            'email' => $mysqlUser->email ?? '<EMAIL>',
            'phone' => $mysqlUser->telephone ?? null,
            'password' => $mysqlUser->password,
            'role_id' => $roleId,
            'dealer_id' => $dealerId,
            'region_id' => $regionId,
            'email_verified_at' => $mysqlUser->email_verified_at ?? null,
            'remember_token' => $mysqlUser->remember_token ?? null,
            'created_at' => $mysqlUser->created_at ?? now(),
            'updated_at' => $mysqlUser->updated_at ?? now(),
        ];
    }
}
