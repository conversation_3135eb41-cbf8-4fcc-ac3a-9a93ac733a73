<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Role;
use App\Models\Dealer;
use Illuminate\Support\Facades\Hash;

class UserManagementController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        try {
            $users = User::with('role', 'dealer')->paginate(10);
            return view('user_management.index', compact('users'));
        } catch (\Exception $e) {
            return redirect()->route('dashboard')
                ->with('error', 'Kullanıcı listesi yüklenirken hata oluştu: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        try {
            $roles = Role::all();
            if ($roles->isEmpty()) {
                return redirect()->route('user-management.index')
                    ->with('warning', 'Henüz rol tanımlanmamış. Önce rolleri ekleyin.');
            }

            $dealers = Dealer::active()->with('region')->orderBy('name')->get();

            return view('user_management.create', compact('roles', 'dealers'));
        } catch (\Exception $e) {
            return redirect()->route('user-management.index')
                ->with('error', 'Kullanıcı ekleme sayfası yüklenirken hata oluştu: ' . $e->getMessage());
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'phone' => 'required|string|max:20|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role_id' => 'required|exists:roles,id',
            'dealer_id' => 'nullable|exists:dealers,id',
            'region_id' => 'nullable|exists:regions,id',
        ]);

        $validated['password'] = Hash::make($validated['password']);

        User::create($validated);

        return redirect()->route('user-management.index')
            ->with('success', 'Kullanıcı başarıyla oluşturuldu.');
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        $user->load('role', 'dealer.region');
        return view('user_management.show', compact('user'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        $roles = Role::all();
        $dealers = Dealer::active()->with('region')->orderBy('name')->get();
        return view('user_management.edit', compact('user', 'roles', 'dealers'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'required|string|max:20|unique:users,phone,' . $user->id,
            'role_id' => 'required|exists:roles,id',
            'dealer_id' => 'nullable|exists:dealers,id',
            'region_id' => 'nullable|exists:regions,id',
        ]);

        // Şifre güncellenmek isteniyorsa
        if ($request->filled('password')) {
            $request->validate([
                'password' => 'required|string|min:8|confirmed',
            ]);
            $validated['password'] = Hash::make($request->password);
        }

        $user->update($validated);

        return redirect()->route('user-management.index')
            ->with('success', 'Kullanıcı başarıyla güncellendi.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        $user->delete();

        return redirect()->route('user-management.index')
            ->with('success', 'Kullanıcı başarıyla silindi.');
    }
}
