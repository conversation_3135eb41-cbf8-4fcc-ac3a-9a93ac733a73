<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            if (!Schema::hasColumn('users', 'region_id')) {
                $table->foreignId('region_id')->nullable()->constrained('regions')->onDelete('set null')->after('dealer_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn('users', 'region_id')) {
                // PostgreSQL özel SQL ile constraint adı kontrol edilerek silinir
                DB::statement('ALTER TABLE users DROP CONSTRAINT IF EXISTS users_region_id_foreign');
                $table->dropColumn('region_id');
            }
        });
    }
};
