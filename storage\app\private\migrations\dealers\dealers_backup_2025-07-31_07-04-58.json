{"timestamp": "2025-07-31_07-04-58", "total_dealers": 144, "dealers": [{"id": 1, "region_id": 1, "name": "<PERSON><PERSON><PERSON>", "contact_person": "2023-12-12 00:00:00", "phone": "<PERSON><PERSON>", "email": "<EMAIL>", "address": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>", "city": "58", "district": "<PERSON><PERSON>", "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 2, "region_id": 1, "name": "İstanbul Automall", "contact_person": "<PERSON><PERSON>", "phone": "0551 257 72 22", "email": "<EMAIL>", "address": null, "city": "34", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 3, "region_id": 1, "name": "Umran Plus", "contact_person": "Bilinmeyen Kişi", "phone": null, "email": "<EMAIL>", "address": null, "city": "34", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 4, "region_id": 1, "name": "İstanbul Otoport", "contact_person": "<PERSON>", "phone": "0532 062 40 09", "email": "<EMAIL>", "address": "<PERSON><PERSON><PERSON>, Otoport Avm No:46/121-122, 68. <PERSON><PERSON><PERSON>", "city": "34", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 5, "region_id": 1, "name": "Şanlıurfa Evren Sanayi", "contact_person": "Furkan Pektaş", "phone": "0531 517 73 43", "email": "<EMAIL>", "address": "Batıkent Mah., İpekyolu Bulvarı No:306/1, Evren Sanayi Sitesi", "city": "63", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 6, "region_id": 1, "name": "Elazığ", "contact_person": "<PERSON><PERSON><PERSON><PERSON>", "phone": "0532 739 31 51", "email": "<EMAIL>", "address": null, "city": "23", "district": "Sürsürü", "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 7, "region_id": 1, "name": "Diyarbakır Ye<PERSON>şehir", "contact_person": "İhsan YARAMIŞLI", "phone": "5058303108", "email": "<EMAIL>", "address": "Fabrika <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>.<PERSON>us <PERSON> No:167", "city": "21", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 8, "region_id": 1, "name": "İstanbul Göztepe", "contact_person": "<PERSON><PERSON>", "phone": "0536 975 44 89", "email": "<EMAIL>", "address": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, No:18 B", "city": "34", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 9, "region_id": 1, "name": "İstanbul Autopia", "contact_person": "<PERSON><PERSON>", "phone": "0530 286 19 88", "email": "<EMAIL>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>lv., No: 1/373 Autopia", "city": "34", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 10, "region_id": 1, "name": "Kocaeli İzmit", "contact_person": "Soner GÜNER", "phone": "0530 789 31 41", "email": "<EMAIL>", "address": "Karadenizliler Mah., D 130 Karayolu  üzeri Yanyol Caddesi, No:272", "city": "41", "district": "Başiskele", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 11, "region_id": 1, "name": "Ankara Pursaklar", "contact_person": "<PERSON><PERSON>", "phone": "0546 884 31 11", "email": "<EMAIL>", "address": "<PERSON><PERSON>, <PERSON><PERSON>, 185/8", "city": "6", "district": "<PERSON><PERSON><PERSON><PERSON>", "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 12, "region_id": 1, "name": "İzmir Otokent", "contact_person": "Oğuz Özkan", "phone": "0539 656 72 66", "email": "<EMAIL>", "address": "İnö<PERSON><PERSON>, Osmansergengeçti Cad., 677/19 Sok.No:126", "city": "35", "district": "B<PERSON>", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 13, "region_id": 1, "name": "İstanbul Tepeüstü", "contact_person": "<PERSON><PERSON>", "phone": "0535 674 78 62", "email": "<EMAIL>", "address": "Tepeüstü Mah., Alemdağ Cad., No:552/c", "city": "34", "district": "Tepeüstü", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 14, "region_id": 1, "name": "İstanbul Çobançeşme", "contact_person": "<PERSON><PERSON>", "phone": "0555 007 1277", "email": "<EMAIL>", "address": null, "city": "34", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 15, "region_id": 1, "name": "İzmir Akçay", "contact_person": "Aykut VAR", "phone": "(*************", "email": "<EMAIL>", "address": "Binbaşı Reşatbey Mah., Akçay Cad., No:43 A", "city": "35", "district": "<PERSON><PERSON><PERSON><PERSON>", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 16, "region_id": 1, "name": "İstanbul Maltepe", "contact_person": "Yavuz Dündar", "phone": "0543 935 48 81", "email": "<EMAIL>", "address": null, "city": "34", "district": "<PERSON><PERSON><PERSON><PERSON>", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 17, "region_id": 1, "name": "Konya", "contact_person": "Ertan Ece", "phone": "0552 674 42 42", "email": "<EMAIL>", "address": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, J Blok No:53/1", "city": "42", "district": "Galericiler S.", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 18, "region_id": 1, "name": "Samsun Kirazlık", "contact_person": "<PERSON><PERSON>", "phone": "0552 320 28 28", "email": "<EMAIL>", "address": "Kirazlık Mah., Atatürk Bulv., No:6/1", "city": "55", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 19, "region_id": 1, "name": "Diyarbakır <PERSON> (Kapalı)", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "0552 641 79 21", "email": "<EMAIL>", "address": null, "city": "21", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 20, "region_id": 1, "name": "<PERSON>", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "0543 478 22 72", "email": "<EMAIL>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> Mah., Tpa Bulvarı, B Blok Güçlü Apt.No:233 A", "city": "72", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 21, "region_id": 1, "name": "İstanbul Dudullu", "contact_person": "Bilinmeyen Kişi", "phone": null, "email": "<EMAIL>", "address": "Necip <PERSON> Mah., Alemdağ Cad., No:16 A", "city": "34", "district": "DUDULLU", "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 22, "region_id": 1, "name": "<PERSON><PERSON><PERSON>", "contact_person": "<PERSON><PERSON>", "phone": "0532 732 54 17", "email": "<EMAIL>", "address": "Barbar<PERSON>, <PERSON><PERSON> Bulv., No:470/a", "city": "33", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 23, "region_id": 1, "name": "<PERSON><PERSON><PERSON>", "contact_person": "<PERSON><PERSON>", "phone": "0553 035 58 74", "email": "<EMAIL>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> Mah., Aspendos Bulv., No:212 B", "city": "7", "district": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 24, "region_id": 1, "name": "İstanbul Tuzla", "contact_person": "<PERSON><PERSON>", "phone": "0536 449 10 55", "email": "<EMAIL>", "address": "Aydıntepe Mah., Aydınlı <PERSON>, 19/a", "city": "34", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 25, "region_id": 1, "name": "İstanbul Maslak", "contact_person": "<PERSON><PERSON>", "phone": "0532 562 63 09", "email": "<EMAIL>", "address": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> Evran <PERSON>. No:25 J-, 13. Sk", "city": "34", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 26, "region_id": 1, "name": "<PERSON><PERSON><PERSON>", "contact_person": "<PERSON><PERSON>", "phone": "05324194154", "email": "<EMAIL>", "address": "Yeşiltepe Mah., Orhangazi Cad., No:203/1", "city": "54", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 27, "region_id": 1, "name": "<PERSON><PERSON>", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "0535 633 22 47", "email": "<EMAIL>", "address": "Yenikent Mah., <PERSON>din <PERSON>., No:351 D Havalimanı Karşısı", "city": "47", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 28, "region_id": 1, "name": "Kırklareli Lüleburgaz", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "0533 413 44 26", "email": "<EMAIL>", "address": "Atatürk Mah., <PERSON><PERSON>, 4.Sok.C Blok No:62", "city": "39", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 29, "region_id": 1, "name": "Ankara Şaşmaz", "contact_person": "Burak Üçkol", "phone": "0555 101 54 17", "email": null, "address": "Bahçekapı Mah., Sanayi Bulv., No:7 E", "city": "6", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 30, "region_id": 1, "name": "İstanbul Zeytinburnu", "contact_person": "<PERSON><PERSON>", "phone": "0546 238 33 37", "email": "z<PERSON><EMAIL>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, No: 77/E", "city": "34", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 31, "region_id": 1, "name": "<PERSON>", "contact_person": "<PERSON>", "phone": "0501 155 65 65", "email": "<EMAIL>", "address": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, No:113 A", "city": "65", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 32, "region_id": 1, "name": "<PERSON><PERSON><PERSON>", "contact_person": "<PERSON><PERSON>", "phone": "0533 541 61 77", "email": "<EMAIL>", "address": null, "city": "7", "district": "<PERSON><PERSON>", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 33, "region_id": 1, "name": "<PERSON><PERSON><PERSON>", "contact_person": "<PERSON><PERSON>", "phone": "0554 474 17 00", "email": "<EMAIL>", "address": null, "city": "20", "district": "Merkezefendi", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 34, "region_id": 1, "name": "İstanbul Üsküdar", "contact_person": "Erkan <PERSON>", "phone": "0552 442 54 17", "email": "<EMAIL>", "address": "Kısıklı Mah., Alemdağ Cad., No:24", "city": "34", "district": "Çamlıca", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 35, "region_id": 1, "name": "Eskişehir", "contact_person": "<PERSON><PERSON><PERSON><PERSON>", "phone": "(0532) 419 2126", "email": "<EMAIL>", "address": null, "city": "26", "district": "Odunpazarı", "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 36, "region_id": 1, "name": "<PERSON><PERSON><PERSON>", "contact_person": "<PERSON>", "phone": "(*************", "email": "<EMAIL>", "address": null, "city": "7", "district": "Kepez", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 37, "region_id": 1, "name": "İstanbul Sultangazi", "contact_person": "<PERSON><PERSON>", "phone": "0534 861 99 41", "email": "<EMAIL>", "address": "Malkoçoğlu Mah., Mahmutbey Cad., No:18", "city": "34", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 38, "region_id": 1, "name": "Ankara Ostim", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "0552 738 86 76", "email": "<EMAIL>", "address": null, "city": "6", "district": "<PERSON><PERSON><PERSON>", "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 39, "region_id": 1, "name": "Ankara Sincan", "contact_person": "Melek <PERSON>lu", "phone": "0552 830 30 15", "email": "<EMAIL>", "address": null, "city": "6", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 40, "region_id": 1, "name": "Ankara Kazımkarabekir", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "0532 286 65 85", "email": "<EMAIL>", "address": "Hacıbayram Mah., Kazımkarabekir Cad., No:122/24", "city": "6", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 41, "region_id": 1, "name": "<PERSON><PERSON><PERSON>", "contact_person": "<PERSON><PERSON>", "phone": "0539 546 06 93", "email": "<EMAIL>", "address": "Üçevler Mah., Ahiska Cad., No:2 Akgün İş Mer.", "city": "16", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 42, "region_id": 1, "name": "Afyonkarahisar", "contact_person": "<PERSON><PERSON>", "phone": "0533 659 09 07", "email": "<EMAIL>", "address": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> Bulv., No : 61 / F", "city": "3", "district": "<PERSON><PERSON><PERSON>", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 43, "region_id": 1, "name": "İstanbul Sultanbeyli", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "0530 311 66 65", "email": "<EMAIL>", "address": "<PERSON><PERSON><PERSON>, Atatürk Caddesi, No 66/B", "city": "34", "district": "Sultanbeyli Metro İstasyonu önü", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 44, "region_id": 1, "name": "Tekirdağ Çorlu", "contact_person": "<PERSON><PERSON>", "phone": "0553 982 41 61", "email": "<EMAIL>", "address": null, "city": "59", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 45, "region_id": 1, "name": "<PERSON><PERSON><PERSON>", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "0533 351 33 48", "email": "<EMAIL>", "address": "Ka<PERSON><PERSON><PERSON><PERSON><PERSON> Ma<PERSON>, Zeki Ergezen Bulvarı A B C, Girişi Bloklar No:17 K", "city": "12", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 46, "region_id": 1, "name": "Şanlıurfa Siverek", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "0532 770 02 91", "email": "<EMAIL>", "address": null, "city": "63", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 47, "region_id": 1, "name": "Trabzon", "contact_person": "<PERSON><PERSON>", "phone": "05497926161", "email": "<EMAIL>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON> Evleri, No:10/ <PERSON>ç Kapı  No:3", "city": "61", "district": "<PERSON><PERSON><PERSON>", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 48, "region_id": 1, "name": "<PERSON><PERSON><PERSON>", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "0532 498 94 17", "email": "<EMAIL>", "address": null, "city": "35", "district": "BORNOVA", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 49, "region_id": 1, "name": "İzmir Çiğli", "contact_person": "<PERSON><PERSON>", "phone": "0553 240 40 55", "email": "<EMAIL>", "address": null, "city": "35", "district": "ÇİĞLİ", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 50, "region_id": 1, "name": "<PERSON><PERSON><PERSON>", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "0539 372 8090", "email": "<EMAIL>", "address": null, "city": "7", "district": "<PERSON><PERSON>", "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 51, "region_id": 1, "name": "İstanbul Silivri", "contact_person": "Ertuğ<PERSON><PERSON>", "phone": "0533 374 53 34", "email": "<EMAIL>", "address": null, "city": "34", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 52, "region_id": 1, "name": "Ankara Balgat", "contact_person": "<PERSON><PERSON>", "phone": "(0552) 657 8868", "email": "<EMAIL>", "address": null, "city": "6", "district": "Balgat", "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 53, "region_id": 1, "name": "Gaziantep ARV", "contact_person": "Furkan Arvas", "phone": "0530 069 93 65", "email": "<EMAIL>", "address": null, "city": "27", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 54, "region_id": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "contact_person": "İSA ÖZKAN", "phone": "05454905726", "email": "<EMAIL>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Mah., <PERSON>z<PERSON> Caddesi, 2664 sokak  No:1/A  ( <PERSON><PERSON><PERSON> )", "city": "9", "district": "<PERSON><PERSON><PERSON>", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 55, "region_id": 1, "name": "Şanlıurfa Haliliye", "contact_person": "<PERSON>", "phone": "0546 152 63 63", "email": "<EMAIL>", "address": "Paşabağı Mah., Zafer Cad., No:17 B", "city": "63", "district": "Şairnabi", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 56, "region_id": 1, "name": "İstanbul Pendik", "contact_person": "<PERSON><PERSON>", "phone": "05422504653", "email": "<EMAIL>", "address": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> Mehm<PERSON> Caddesi, No 97", "city": "34", "district": "<PERSON><PERSON><PERSON><PERSON>", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 57, "region_id": 1, "name": "İstanbul Samandıra", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "05427425095", "email": "<EMAIL>", "address": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>kak No:40/27   OTOMER GALERİCİLER SİTESİ", "city": "34", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 58, "region_id": 1, "name": "Ankara Koru Kavşağı", "contact_person": "<PERSON><PERSON><PERSON><PERSON>", "phone": "0542 810 23 06", "email": null, "address": null, "city": "6", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 59, "region_id": 1, "name": "<PERSON><PERSON>", "contact_person": "Gürbüz Bulut", "phone": "0536 391 90 30", "email": "<EMAIL>", "address": null, "city": "45", "district": "<PERSON><PERSON><PERSON><PERSON>", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 60, "region_id": 1, "name": "Balıkesir", "contact_person": "Ahmet ATALAY", "phone": "(*************", "email": "<EMAIL>", "address": "Pa<PERSON><PERSON>, Cumhuriyet Cad. No:57, <PERSON><PERSON><PERSON>", "city": "10", "district": "Paşalar", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 61, "region_id": 1, "name": "Samsun İlkadım", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "0505 542 24 18", "email": "<EMAIL>", "address": "Derecik Mah., No:1 İç Kapı No:1, Anadolu Blv", "city": "55", "district": "Yeşilkent", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 62, "region_id": 1, "name": "Diyarbakır Bağlar", "contact_person": "<PERSON><PERSON>", "phone": "04122355417", "email": "<EMAIL>", "address": "Talaytepe, Şanlıurfa Bulvarı, 21070", "city": "21", "district": "Bağlar", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 63, "region_id": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "contact_person": "Alper Öksüz", "phone": "0531 990 65 99", "email": "ka<PERSON><PERSON><PERSON><PERSON>@umranotobayi.com", "address": "Barbaros Ma<PERSON>., <PERSON><PERSON><PERSON>, 11032. Sokak No:1/a", "city": "46", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 64, "region_id": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "contact_person": "<PERSON><PERSON>", "phone": "5411064068", "email": "<EMAIL>", "address": "<PERSON><PERSON><PERSON><PERSON>rmanı Mah., Toptanc<PERSON>lar Sitesi, 5736 Sokak", "city": "68", "district": "<PERSON><PERSON><PERSON><PERSON>", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 65, "region_id": 1, "name": "<PERSON><PERSON><PERSON>", "contact_person": "Oğuzhan Aymaz", "phone": "0545 273 37 07", "email": "<EMAIL>", "address": "İstasyon Mah., E-5 <PERSON><PERSON><PERSON>, No: 106", "city": "41", "district": "<PERSON><PERSON><PERSON>", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 66, "region_id": 1, "name": "İstanbul Kadıköy (Kapalı)", "contact_person": "Bilinmeyen Kişi", "phone": null, "email": null, "address": null, "city": "34", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 67, "region_id": 1, "name": "İstanbul Bayrampaşa", "contact_person": "<PERSON><PERSON>", "phone": "0533 023 27 74", "email": "<EMAIL>", "address": null, "city": "34", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 68, "region_id": 1, "name": "Adıyaman", "contact_person": "<PERSON><PERSON>", "phone": "0552 816 72 08", "email": "<EMAIL>", "address": null, "city": "2", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 69, "region_id": 1, "name": "İstanbul İstoç", "contact_person": "Akın <PERSON>", "phone": "0552 480 59 52", "email": "<EMAIL>", "address": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>., A-b Blok No: 155-157 A", "city": "34", "district": "Bağcılar", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 70, "region_id": 1, "name": "İstanbul Kağıthane", "contact_person": "<PERSON><PERSON>", "phone": "5436658830", "email": "<EMAIL>", "address": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>., No : 11A", "city": "34", "district": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 71, "region_id": 1, "name": "İstanbul Güngören", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "0536 952 00 48", "email": "<EMAIL>", "address": null, "city": "34", "district": "<PERSON><PERSON><PERSON>", "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 72, "region_id": 1, "name": "İstanbul Firüzköy", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "0546 915 12 05", "email": "<EMAIL>", "address": "Firuzköy Mah., Firuzköy Bulv., No: 148", "city": "34", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 73, "region_id": 1, "name": "<PERSON><PERSON>", "contact_person": "<PERSON><PERSON><PERSON><PERSON>", "phone": "0533 678 69 70", "email": "<EMAIL>", "address": "<PERSON><PERSON><PERSON><PERSON>, Kamyoncular Sitesi A Blok, 46116 No:4/a", "city": "1", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 74, "region_id": 1, "name": "B<PERSON>a <PERSON>", "contact_person": "<PERSON><PERSON>", "phone": "0553 809 96 13", "email": "<EMAIL>", "address": "Başaran Mah., İstanbul Cad., No: 387 / A", "city": "16", "district": "Bahar", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 75, "region_id": 1, "name": "İstanbul Kartal", "contact_person": "<PERSON><PERSON>", "phone": "0546 259 01 53", "email": "<EMAIL>", "address": null, "city": "34", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 76, "region_id": 1, "name": "<PERSON><PERSON><PERSON>", "contact_person": "<PERSON><PERSON>", "phone": "05393584136", "email": "<EMAIL>", "address": null, "city": "41", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 77, "region_id": 1, "name": "İstanbul İkitelli (Kapalı)", "contact_person": "Bilinmeyen Kişi", "phone": null, "email": null, "address": null, "city": "34", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 78, "region_id": 1, "name": "<PERSON><PERSON>", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "0545 123 86 55", "email": "<EMAIL>", "address": null, "city": "55", "district": "Çarşamba", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 79, "region_id": 1, "name": "Malatya", "contact_person": "Şener Bektaş", "phone": "0533 501 64 44", "email": "<EMAIL>", "address": "Cevatpaşa Mah., Turgut Özal Bulvarı No:52/a, Turgutözal Blv. No:52", "city": "44", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 80, "region_id": 1, "name": "<PERSON><PERSON><PERSON>", "contact_person": "<PERSON>", "phone": "05332378654", "email": "<EMAIL>", "address": null, "city": "54", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 81, "region_id": 1, "name": "<PERSON><PERSON><PERSON>", "contact_person": "Musa Gaspak", "phone": "0534 551 80 84", "email": "<EMAIL>", "address": null, "city": "38", "district": "Argıncık", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 82, "region_id": 1, "name": "Konya Ereğli", "contact_person": "Emre Ponçaklı", "phone": "0553 869 74 55", "email": "<EMAIL>", "address": "<PERSON><PERSON>ö<PERSON>p Mah., Şehit Ömer Halisdemir Cad., No:91 K", "city": "42", "district": "<PERSON><PERSON><PERSON><PERSON>", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 83, "region_id": 1, "name": "<PERSON><PERSON><PERSON>", "contact_person": "<PERSON><PERSON>", "phone": "0535 380 14 39", "email": "<EMAIL>", "address": "Ko<PERSON>ent Mah., 243. Cad., No: 24 / G <PERSON>ç <PERSON> 01", "city": "33", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 84, "region_id": 1, "name": "İstanbul Ispartakule", "contact_person": "<PERSON><PERSON>", "phone": "0542 102 43 38", "email": "<EMAIL>", "address": null, "city": "34", "district": "Gümüşpala", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 85, "region_id": 1, "name": "<PERSON><PERSON>", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "0551 106 93 97", "email": "<EMAIL>", "address": null, "city": "1", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 86, "region_id": 1, "name": "Çorum", "contact_person": "<PERSON><PERSON>", "phone": "0555 898 57 19", "email": "<EMAIL>", "address": null, "city": "19", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 87, "region_id": 1, "name": "Şanlıurfa Viranşehir", "contact_person": "<PERSON><PERSON>", "phone": "0542 763 47 38", "email": "<EMAIL>", "address": null, "city": "63", "district": "Viranşehir", "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 88, "region_id": 1, "name": "<PERSON><PERSON><PERSON>", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "0553 092 46 32", "email": "<EMAIL>", "address": "Beylerbeyi Mah., Denizli Aydın Yolu 1.Cad., No: 39 / B", "city": "20", "district": "Sarayköy", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 89, "region_id": 1, "name": "İstanbul B.Evler <PERSON>ş", "contact_person": "<PERSON><PERSON>", "phone": "0505 945 83 92", "email": "<EMAIL>", "address": "<PERSON><PERSON><PERSON><PERSON> Merkez Mah., 29 Ekim Cd., No:29", "city": "34", "district": "<PERSON><PERSON><PERSON><PERSON>", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 90, "region_id": 1, "name": "Gaziantep Nizip", "contact_person": "<PERSON><PERSON><PERSON><PERSON>", "phone": "0542 517 32 63", "email": "<EMAIL>", "address": null, "city": "27", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 91, "region_id": 1, "name": "Hat<PERSON>", "contact_person": "<PERSON><PERSON>", "phone": "0505 937 63 31", "email": "<EMAIL>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> Hat<PERSON> Yo<PERSON> Ü<PERSON>i, 102/1", "city": "31", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 92, "region_id": 1, "name": "O<PERSON>i<PERSON>", "contact_person": "<PERSON><PERSON>", "phone": "0533 578 84 80", "email": "<EMAIL>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, No:151/e", "city": "80", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 93, "region_id": 1, "name": "Ankara İvedik", "contact_person": "<PERSON><PERSON>", "phone": "0506 048 83 82", "email": "<EMAIL>", "address": null, "city": "6", "district": "İVEDİK OSB", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 94, "region_id": 1, "name": "Kocaeli Gölcük", "contact_person": "<PERSON><PERSON>", "phone": "05307893141", "email": "<EMAIL>", "address": "Çiftlik Mah., Atatürk Bulvarı, No: 339/A", "city": "41", "district": "Gölcük", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 95, "region_id": 1, "name": "Tekirdağ Çerkezköy", "contact_person": "Ertuğ<PERSON><PERSON>", "phone": "0535 939 68 85", "email": "<EMAIL>", "address": "Cumhuriyet Mah., İnönü Cad., A Blok No:34 F", "city": "59", "district": "Çerkezköy", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 96, "region_id": 1, "name": "Kastamonu", "contact_person": "Ümit Sağdıç", "phone": "0545 845 91 91", "email": null, "address": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>., Akveria Akkent No:14 A 1", "city": "37", "district": "Kuzeykent", "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 97, "region_id": 1, "name": "Ordu", "contact_person": "Bilinmeyen Kişi", "phone": null, "email": "<EMAIL>", "address": "Karapınar Mah., Ordu, 1142.sok no:2/a", "city": "52", "district": "2.sa<PERSON>i <PERSON>i", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 98, "region_id": 1, "name": "Bursa Yıldırım", "contact_person": "<PERSON><PERSON>", "phone": "05395460693", "email": "<EMAIL>", "address": null, "city": "16", "district": "Yıldırım", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 99, "region_id": 1, "name": "<PERSON><PERSON>", "contact_person": "<PERSON>", "phone": "0533 946 10 83", "email": "<EMAIL>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>., No:3/2", "city": "13", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 100, "region_id": 1, "name": "İstanbul Florya", "contact_person": "<PERSON><PERSON><PERSON><PERSON> OĞRAŞ", "phone": "0531 499 90 03", "email": "<EMAIL>", "address": "<PERSON><PERSON><PERSON><PERSON>., <PERSON><PERSON> Londra Asfaltı Blv., Soyyiğit İş Merkezi No:16/1b", "city": "34", "district": "Sefaköy", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 101, "region_id": 1, "name": "İstanbul Akbatı", "contact_person": "<PERSON><PERSON>", "phone": "0533 436 61 30", "email": "<EMAIL>", "address": "Koza Mah., Atatürk Bulvarı No:7 I, Atatürk Blv. No:7j", "city": "34", "district": "Esenyurt", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 102, "region_id": 1, "name": "<PERSON><PERSON>", "contact_person": "<PERSON>", "phone": "05374702323", "email": "<EMAIL>", "address": null, "city": "58", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 103, "region_id": 1, "name": "<PERSON><PERSON>", "contact_person": "<PERSON><PERSON><PERSON><PERSON>", "phone": "0551 727 01 01", "email": "<EMAIL>", "address": "FEVZİPAŞA MAH., Vefa Cad. No: 35/a, Ankarayolu Cd.", "city": "1", "district": "FEVZİPAŞA", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 104, "region_id": 1, "name": "İstanbul Kavacık", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "0506 588 67 26", "email": "<EMAIL>", "address": "Kavacık Mah., Serpil Sokak No:19, Serpil Sk. No:19", "city": "34", "district": "Anadoluhisarı", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 105, "region_id": 1, "name": "İstanbul İstoç O<PERSON>", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "0507 243 00 20", "email": "<EMAIL>", "address": "Göztepe Mah., İstoç 2.Cad<PERSON>, A4 Blok No:1/2", "city": "34", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 106, "region_id": 1, "name": "<PERSON>ev<PERSON><PERSON><PERSON>", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "(0535) 607 6050", "email": "<EMAIL>", "address": null, "city": "50", "district": "<PERSON><PERSON><PERSON>", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 107, "region_id": 1, "name": "İstanbul Bostancı", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "05540286726", "email": "<EMAIL>", "address": null, "city": "34", "district": "Bostancı Oto <PERSON>ayi", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 108, "region_id": 1, "name": "<PERSON><PERSON>", "contact_person": "Bilinmeyen Kişi", "phone": null, "email": "<EMAIL>", "address": null, "city": "34", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 109, "region_id": 1, "name": "<PERSON><PERSON>", "contact_person": "Bilinmeyen Kişi", "phone": null, "email": null, "address": null, "city": "34", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 110, "region_id": 1, "name": "<PERSON><PERSON><PERSON>", "contact_person": "Bilinmeyen Kişi", "phone": null, "email": null, "address": null, "city": "34", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 111, "region_id": 1, "name": "Umran Grup", "contact_person": "Bilinmeyen Kişi", "phone": null, "email": null, "address": null, "city": "1", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 112, "region_id": 1, "name": "<PERSON><PERSON><PERSON>", "contact_person": "AHMET YILMAZ", "phone": "05559759861", "email": "<EMAIL>", "address": "Bayraktepe Mah., Yalova Bursa Yolu Cad., A Blok No:110/1 <PERSON>ç <PERSON>ı No:6", "city": "77", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 113, "region_id": 1, "name": "<PERSON><PERSON><PERSON>", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "(0531) 716 9142", "email": "<EMAIL>", "address": null, "city": "7", "district": "<PERSON><PERSON><PERSON>", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 114, "region_id": 1, "name": "Kayışdağı (Kapalı)", "contact_person": "<PERSON><PERSON>", "phone": "0532 653 82 09", "email": null, "address": "Kayışdağı Mah., Kayışdağı Cad., No:170/a", "city": "34", "district": "Kayışdağı", "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 115, "region_id": 1, "name": "Yozgat (Kapalı)", "contact_person": "<PERSON><PERSON>", "phone": "0546 435 14 90", "email": null, "address": null, "city": "66", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 116, "region_id": 1, "name": "İstanbul Sütlüce(Kapalı)", "contact_person": "Bilinmeyen Kişi", "phone": null, "email": null, "address": null, "city": "34", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 117, "region_id": 1, "name": "İstanbul Oto Center(Kapalı)", "contact_person": "Bilinmeyen Kişi", "phone": null, "email": null, "address": null, "city": "34", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 118, "region_id": 1, "name": "Konya Akşehir", "contact_person": "<PERSON><PERSON>", "phone": "05412039348", "email": "<EMAIL>", "address": null, "city": "42", "district": "Yeni Sanayi Sitesi Akşehir", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 119, "region_id": 1, "name": "İstanbul Büyükçekmece(Kapalı)", "contact_person": "Bilinmeyen Kişi", "phone": null, "email": null, "address": null, "city": "34", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 120, "region_id": 1, "name": "<PERSON><PERSON><PERSON>", "contact_person": "<PERSON><PERSON>", "phone": "05322098774", "email": "<EMAIL>", "address": "<PERSON><PERSON>en <PERSON>. <PERSON><PERSON>, Aşağı Düz Cad., No: 95 H", "city": "74", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 121, "region_id": 1, "name": "Kilis (Kapalı)", "contact_person": "Bilinmeyen Kişi", "phone": null, "email": null, "address": null, "city": "79", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 122, "region_id": 1, "name": "İst. Arnavutköy (Kapalı)", "contact_person": "Bilinmeyen Kişi", "phone": null, "email": null, "address": null, "city": "34", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 123, "region_id": 1, "name": "Test", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "0533 351 33 48", "email": null, "address": null, "city": "1", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 124, "region_id": 1, "name": "<PERSON><PERSON> ad<PERSON>(Kapalı)", "contact_person": "Bilinmeyen Kişi", "phone": null, "email": null, "address": null, "city": "1", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 125, "region_id": 1, "name": "Test Bayi", "contact_person": "Bilinmeyen Kişi", "phone": null, "email": null, "address": null, "city": "1", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 126, "region_id": 1, "name": "Bursa O<PERSON>gazi (Kapalı)", "contact_person": "Burak Ağbulak", "phone": "0533 510 32 02", "email": "<EMAIL>", "address": null, "city": "16", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 127, "region_id": 1, "name": "İstanbul İstoç ( Kapalı )", "contact_person": "Burak Ağbulak", "phone": "0533 510 32 02", "email": "<EMAIL>", "address": null, "city": "1", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 128, "region_id": 1, "name": "İstanbul Bayrampaşa (kapalı)", "contact_person": "Ülkü Boğuk", "phone": "0541 597 68 43", "email": "<EMAIL>", "address": null, "city": "34", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 129, "region_id": 1, "name": "İstanbul Florya (kapalı)", "contact_person": "Uğur Yıldırım", "phone": "0536 798 79 73", "email": null, "address": null, "city": "1", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 130, "region_id": 1, "name": "Ankara Autozone", "contact_person": "SİNAN MACAR", "phone": "(*************", "email": "<EMAIL>", "address": null, "city": "6", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 131, "region_id": 1, "name": "İstanbul Kayışdağı", "contact_person": "Özkan Başgedik", "phone": "0506 815 16 98", "email": "<EMAIL>", "address": "inön<PERSON> mahallesi, kayışdağı caddesi, no 282 -284 A", "city": "34", "district": "EMİN PLAZA", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 132, "region_id": 1, "name": "Elazığ Çarşı", "contact_person": "<PERSON><PERSON>", "phone": "5521812323", "email": "<EMAIL>", "address": null, "city": "23", "district": "<PERSON><PERSON><PERSON>", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 133, "region_id": 1, "name": "<PERSON><PERSON>", "contact_person": "Ufuk Yanık", "phone": "05055611290", "email": "<EMAIL>", "address": null, "city": "14", "district": "<PERSON><PERSON>", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 134, "region_id": 1, "name": "BAYİ DEMO", "contact_person": "Bilinmeyen Kişi", "phone": null, "email": null, "address": "3.<PERSON><PERSON><PERSON><PERSON>, 5.<PERSON><PERSON><PERSON>, 1247 SOKAK", "city": "34", "district": "NEW YORK", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 135, "region_id": 1, "name": "Düzce", "contact_person": "FATİH YILMAZ", "phone": "05545678411", "email": "<EMAIL>", "address": null, "city": "81", "district": "<PERSON><PERSON><PERSON>", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 136, "region_id": 1, "name": "Ankara Şaşmaz (Yeni)", "contact_person": "<PERSON><PERSON><PERSON><PERSON>", "phone": "05072037801", "email": "<EMAIL>", "address": "Bahçekapı Mah., 2464. Cadde, No:5 Etimesgut Ankara", "city": "6", "district": "Şaşmaz", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 137, "region_id": 1, "name": "Ankara Esenboğa", "contact_person": "<PERSON><PERSON>", "phone": "05468843111", "email": "<EMAIL>", "address": "Saracalar Mah., <PERSON><PERSON>, No:200/1 A Esenboğa Akyurt Ankara", "city": "6", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 138, "region_id": 1, "name": "Gaziantep Günsev", "contact_person": "ABDÜLKADİR ÖZÇUBUKÇU", "phone": "0505 937 63 31", "email": null, "address": "Karacaahmet Mah., 38087 Cad., No:5/48", "city": "27", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 139, "region_id": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "contact_person": "<PERSON><PERSON>", "phone": "05320580864", "email": null, "address": "Şükrüpaşa Mah., Tortum Yolu Cad. No:127, No:127", "city": "25", "district": "<PERSON><PERSON><PERSON><PERSON>", "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 140, "region_id": 1, "name": "<PERSON><PERSON><PERSON>", "contact_person": "MEHMET TÜRKÖZ", "phone": "(*************", "email": "<EMAIL>", "address": null, "city": "51", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 141, "region_id": 1, "name": "Kastamonu İnönü", "contact_person": "EREN TÜLÜBAŞ", "phone": "(0532) 347 0495", "email": null, "address": null, "city": "37", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 142, "region_id": 1, "name": "İstanbul İkitelli", "contact_person": "Ercan <PERSON>", "phone": "(*************", "email": "<EMAIL>", "address": "İkitelli OSB Mah., <PERSON><PERSON><PERSON>, Akyapı No:23/9", "city": "34", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 143, "region_id": 1, "name": "İzmir Beyazevler", "contact_person": "<PERSON><PERSON><PERSON>", "phone": "(0232) 342 5417", "email": null, "address": null, "city": "35", "district": null, "status": true, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}, {"id": 144, "region_id": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "contact_person": "SELİM FALAY", "phone": "(*************", "email": null, "address": null, "city": "2", "district": null, "status": false, "created_at": "2025-07-31T06:18:11.000000Z", "updated_at": "2025-07-31T06:18:11.000000Z", "region": {"id": 1, "name": "Marmara", "description": null, "status": true, "created_at": "2025-07-31T06:17:59.000000Z", "updated_at": "2025-07-31T06:17:59.000000Z"}, "customers": []}], "metadata": {"php_version": "8.4.10", "laravel_version": "12.20.0", "database_connection": "pgsql", "created_by": "DealerMigrationService"}}