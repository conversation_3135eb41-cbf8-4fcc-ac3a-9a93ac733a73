<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'password',
        'role_id',
        'dealer_id', // Primary dealer (backward compatibility)
        'region_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get the role that owns the user.
     */
    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    /**
     * Get the dealer that owns the user.
     */
    public function dealer()
    {
        return $this->belongsTo(Dealer::class);
    }

    /**
     * Get the region that owns the user.
     */
    public function region()
    {
        return $this->belongsTo(Region::class);
    }

    /**
     * Get the dealers for the user (many-to-many relationship).
     */
    public function dealers()
    {
        return $this->belongsToMany(Dealer::class, 'user_dealers');
    }

    /**
     * Get the user dealers pivot records.
     */
    public function userDealers()
    {
        return $this->hasMany(UserDealer::class);
    }

    /**
     * Check if user has a specific role
     */
    public function hasRole($role)
    {
        return $this->role && $this->role->name === $role;
    }

    /**
     * Check if user is admin
     */
    public function isAdmin()
    {
        return $this->hasRole(Role::ADMIN);
    }

    /**
     * Check if user is manager
     */
    public function isManager()
    {
        return $this->hasRole(Role::MANAGER);
    }

    /**
     * Check if user is branch manager
     */
    public function isBranchManager()
    {
        return $this->hasRole(Role::BRANCH_MANAGER);
    }

    /**
     * Check if user is sales representative
     */
    public function isSalesRepresentative()
    {
        return $this->hasRole(Role::SALES_REPRESENTATIVE);
    }

    /**
     * Check if user is regional manager
     */
    public function isRegionalManager()
    {
        return $this->hasRole(Role::REGIONAL_MANAGER);
    }

    /**
     * Check if user is sales manager
     */
    public function isSalesManager()
    {
        return $this->hasRole(Role::SALES_MANAGER);
    }

    /**
     * Check if user is service manager
     */
    public function isServiceManager()
    {
        return $this->hasRole(Role::SERVICE_MANAGER);
    }

    /**
     * Check if user is vehicle reception
     */
    public function isVehicleReception()
    {
        return $this->hasRole(Role::VEHICLE_RECEPTION);
    }
}
