<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use App\Models\RegionDealer;
use App\Models\Region;
use App\Models\Dealer;
use Exception;
use Carbon\Carbon;

class RegionDealerMigrationService
{
    private $backupPath = 'migrations/region_dealers';

    /**
     * MySQL'den zone_branches verilerini çek
     */
    public function fetchMySQLZoneBranches(): array
    {
        try {
            return DB::connection('mysql')
                ->table('zone_branches')
                ->select('*')
                ->orderBy('id')
                ->get()
                ->toArray();
        } catch (Exception $e) {
            throw new Exception('MySQL\'den zone_branches verisi çekme hatası: ' . $e->getMessage());
        }
    }

    /**
     * Mevcut PostgreSQL region_dealers verilerini yedekle
     */
    public function backupCurrentRegionDealers(): string
    {
        $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
        $backupFile = "{$this->backupPath}/region_dealers_backup_{$timestamp}.json";

        $currentRegionDealers = RegionDealer::with(['region', 'dealer'])->get()->toArray();
        
        $backupData = [
            'timestamp' => $timestamp,
            'total_region_dealers' => count($currentRegionDealers),
            'region_dealers' => $currentRegionDealers,
            'metadata' => [
                'php_version' => PHP_VERSION,
                'laravel_version' => app()->version(),
                'database_connection' => config('database.default'),
                'created_by' => 'RegionDealerMigrationService',
            ]
        ];

        Storage::put($backupFile, json_encode($backupData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        return $backupFile;
    }

    /**
     * Yedekten geri yükle
     */
    public function restoreFromBackup(string $backupFile): bool
    {
        if (!Storage::exists($backupFile)) {
            throw new Exception("Yedek dosyası bulunamadı: {$backupFile}");
        }

        $backupData = json_decode(Storage::get($backupFile), true);
        
        if (!$backupData || !isset($backupData['region_dealers'])) {
            throw new Exception('Geçersiz yedek dosyası formatı');
        }

        DB::beginTransaction();
        try {
            // Mevcut verileri sil
            RegionDealer::truncate();

            // Yedekten verileri geri yükle
            foreach ($backupData['region_dealers'] as $regionDealerData) {
                RegionDealer::create([
                    'region_id' => $regionDealerData['region_id'],
                    'dealer_id' => $regionDealerData['dealer_id'],
                    'created_at' => $regionDealerData['created_at'],
                    'updated_at' => $regionDealerData['updated_at'],
                ]);
            }

            DB::commit();
            return true;
        } catch (Exception $e) {
            DB::rollback();
            throw new Exception('Geri yükleme hatası: ' . $e->getMessage());
        }
    }

    /**
     * Yedek dosyalarını listele
     */
    public function listBackups(): array
    {
        $files = Storage::files($this->backupPath);
        $backups = [];

        foreach ($files as $file) {
            if (str_contains($file, 'region_dealers_backup_') && str_ends_with($file, '.json')) {
                $backups[] = [
                    'file' => $file,
                    'name' => basename($file),
                    'size' => Storage::size($file),
                    'modified' => Storage::lastModified($file),
                ];
            }
        }

        // En yeni dosyalar önce gelsin
        usort($backups, function ($a, $b) {
            return $b['modified'] <=> $a['modified'];
        });

        return $backups;
    }

    /**
     * Eski yedekleri temizle
     */
    public function cleanOldBackups(int $keepCount = 5): int
    {
        $backups = $this->listBackups();
        $deletedCount = 0;

        if (count($backups) > $keepCount) {
            $toDelete = array_slice($backups, $keepCount);
            
            foreach ($toDelete as $backup) {
                Storage::delete($backup['file']);
                $deletedCount++;
            }
        }

        return $deletedCount;
    }


}
