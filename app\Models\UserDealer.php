<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserDealer extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'dealer_id',
    ];

    /**
     * Get the user that owns the user dealer.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the dealer that owns the user dealer.
     */
    public function dealer()
    {
        return $this->belongsTo(Dealer::class);
    }
}
